/**
 * Express application configuration for the research agent
 * 
 * This file creates the Express app and handles frontend static file serving,
 * corresponding to the Python app.py file.
 */

import express, { type Request, type Response, type NextFunction, type Router } from 'express';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { getConfig } from './config.js';

/**
 * Create Express application with middleware configuration
 */
function createApp(): express.Application {
  const app = express();
  const config = getConfig();

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "https://generativelanguage.googleapis.com"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false
  }));

  // CORS configuration
  app.use(cors({
    origin: process.env['CORS_ORIGIN'] || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  }));

  // Compression middleware
  app.use(compression());

  // Rate limiting
  const limiter = rateLimit({
    windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000'), // 15 minutes
    max: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100'), // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api', limiter);

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Health check endpoint
  app.get('/health', (_req: Request, res: Response) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      uptime: process.uptime()
    });
  });

  // API routes placeholder (LangGraph will handle the actual API)
  app.get('/api/config', (_req: Request, res: Response) => {
    res.json({
      queryGeneratorModel: config.queryGeneratorModel,
      reflectionModel: config.reflectionModel,
      answerModel: config.answerModel,
      numberOfInitialQueries: config.numberOfInitialQueries,
      maxResearchLoops: config.maxResearchLoops
    });
  });

  // Handle POST requests to config endpoint (should be read-only)
  app.post('/api/config', (_req: Request, res: Response) => {
    res.status(400).json({
      error: 'Bad Request',
      message: 'Configuration endpoint is read-only',
      timestamp: new Date().toISOString()
    });
  });

  return app;
}

/**
 * Creates a router to serve the React frontend
 * Corresponds to Python create_frontend_router function
 * 
 * @param buildDir - Path to the React build directory relative to this file
 * @returns Express router serving the frontend
 */
function createFrontendRouter(buildDir: string = '../../../frontend/dist'): Router {
  const router = express.Router();
  
  // Resolve the build path relative to this file
  const buildPath = path.resolve(__dirname, buildDir);
  const staticFilesPath = path.join(buildPath, 'assets'); // Vite uses 'assets' subdir
  
  // Check if build directory exists
  if (!fs.existsSync(buildPath) || !fs.existsSync(path.join(buildPath, 'index.html'))) {
    console.warn(
      `WARN: Frontend build directory not found or incomplete at ${buildPath}. ` +
      `Serving frontend will likely fail.`
    );
    
    // Return a dummy router if build isn't ready
    router.get('*', (_req: Request, res: Response) => {
      res.status(503).send(
        'Frontend not built. Run \'npm run build\' in the frontend directory.'
      );
    });
    
    return router;
  }

  // Serve static assets from /assets
  if (fs.existsSync(staticFilesPath)) {
    router.use('/assets', express.static(staticFilesPath, {
      maxAge: '1y', // Cache assets for 1 year
      etag: true,
      lastModified: true
    }));
  }

  // Handle all other routes (SPA routing)
  router.get('*', (req: Request, res: Response) => {
    const requestedPath = req.path;
    const filePath = path.join(buildPath, requestedPath);
    
    // Check if the requested file exists
    if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
      // Serve the specific file
      res.sendFile(filePath);
    } else {
      // Fallback to index.html for SPA routing
      res.sendFile(path.join(buildPath, 'index.html'));
    }
  });

  return router;
}

/**
 * Configure the complete Express application
 */
function configureApp(): express.Application {
  const app = createApp();

  // Mount the frontend under /app to not conflict with LangGraph API routes
  // This matches the Python implementation
  app.use('/app', createFrontendRouter());

  // 404 handler for unmatched routes
  app.use('*', (req: Request, res: Response) => {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.originalUrl} not found`,
      timestamp: new Date().toISOString()
    });
  });

  // Global error handler
  app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
    console.error('Unhandled error:', err);

    res.status(500).json({
      error: 'Internal Server Error',
      message: process.env['NODE_ENV'] === 'development' ? err.message : 'Something went wrong',
      timestamp: new Date().toISOString()
    });
  });

  return app;
}

// Create and export the configured app
const app = configureApp();

export default app;
export { createFrontendRouter, createApp, configureApp };
