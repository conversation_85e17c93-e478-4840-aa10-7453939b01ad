/**
 * Jest test setup file
 * 
 * This file configures the testing environment and provides global test utilities.
 */

import { jest } from '@jest/globals';

// Extend Jest matchers if needed
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidConfig(): R;
      toBeValidState(): R;
    }
  }
}

// Global test setup
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.GEMINI_API_KEY = 'test-api-key';
  process.env.QUERY_GENERATOR_MODEL = 'test-gemini-query-model';
  process.env.REFLECTION_MODEL = 'test-gemini-reflection-model';
  process.env.ANSWER_MODEL = 'test-gemini-answer-model';
  process.env.NUMBER_OF_INITIAL_QUERIES = '2';
  process.env.MAX_RESEARCH_LOOPS = '1';
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
  jest.restoreAllMocks();
});

// Global test utilities
global.testUtils = {
  // Mock Gemini API response
  createMockGeminiResponse: (content: string, citations: any[] = []) => ({
    candidates: [{
      content: { parts: [{ text: content }] },
      groundingMetadata: {
        groundingSupports: citations,
        groundingChunks: []
      }
    }]
  }),

  // Mock message objects
  createMockMessage: (content: string, type: 'human' | 'ai' = 'human') => ({
    content,
    getType: () => type,
    _getType: () => type,
    id: Math.random().toString(36),
    name: undefined,
    additional_kwargs: {}
  }),

  // Mock state objects
  createMockState: (overrides: any = {}) => ({
    messages: [],
    searchQuery: [],
    webResearchResult: [],
    sourcesGathered: [],
    initialSearchQueryCount: 3,
    maxResearchLoops: 2,
    researchLoopCount: 0,
    reasoningModel: 'test-model',
    ...overrides
  }),

  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate test data
  generateTestQueries: (count: number = 3) => 
    Array.from({ length: count }, (_, i) => `test query ${i + 1}`),

  generateTestSources: (count: number = 2) =>
    Array.from({ length: count }, (_, i) => ({
      label: `Source ${i + 1}`,
      shortUrl: `https://test.com/short/${i + 1}`,
      value: `https://test.com/original/${i + 1}`
    }))
};

// Custom Jest matchers
expect.extend({
  toBeValidConfig(received) {
    const requiredFields = [
      'queryGeneratorModel',
      'reflectionModel', 
      'answerModel',
      'numberOfInitialQueries',
      'maxResearchLoops'
    ];

    const missingFields = requiredFields.filter(field => !(field in received));
    
    if (missingFields.length > 0) {
      return {
        message: () => `Expected valid config but missing fields: ${missingFields.join(', ')}`,
        pass: false
      };
    }

    return {
      message: () => 'Expected invalid config but received valid config',
      pass: true
    };
  },

  toBeValidState(received) {
    const requiredFields = [
      'messages',
      'searchQuery',
      'webResearchResult',
      'sourcesGathered'
    ];

    const missingFields = requiredFields.filter(field => !(field in received));
    
    if (missingFields.length > 0) {
      return {
        message: () => `Expected valid state but missing fields: ${missingFields.join(', ')}`,
        pass: false
      };
    }

    return {
      message: () => 'Expected invalid state but received valid state',
      pass: true
    };
  }
});

// Suppress console logs during tests unless explicitly needed
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Restore console for specific tests that need it
global.restoreConsole = () => {
  global.console = originalConsole;
};

export {};
