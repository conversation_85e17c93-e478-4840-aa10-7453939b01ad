/**
 * Mock API responses for testing
 * 
 * Provides mock responses for external APIs and services.
 */

import { jest } from '@jest/globals';
import { testSources, testGeminiResponses } from './test-data.js';

// Mock Gemini API client
export const createMockGeminiClient = () => ({
  getGenerativeModel: jest.fn().mockReturnValue({
    generateContent: jest.fn(),
    withStructuredOutput: jest.fn().mockReturnValue({
      invoke: jest.fn()
    })
  })
});

// Mock Lang<PERSON>hain Google GenAI
export const createMockChatGoogleGenerativeAI = () => ({
  invoke: jest.fn(),
  withStructuredOutput: jest.fn().mockReturnValue({
    invoke: jest.fn()
  }),
  bind: jest.fn(),
  pipe: jest.fn()
});

// Mock successful Gemini responses
export const mockGeminiResponses = {
  // Query generation response
  queryGeneration: {
    candidates: [{
      content: {
        parts: [{
          text: JSON.stringify({
            query: ['AI definition 2024', 'artificial intelligence applications'],
            rationale: 'These queries will help gather current information about AI'
          })
        }]
      },
      groundingMetadata: {
        groundingSupports: [],
        groundingChunks: []
      }
    }]
  },

  // Web search response with grounding
  webSearch: {
    candidates: [{
      content: {
        parts: [{
          text: 'Artificial Intelligence (AI) is the simulation of human intelligence processes by machines. Modern AI systems can learn, reason, and make decisions.'
        }]
      },
      groundingMetadata: {
        groundingSupports: [
          {
            segment: { startIndex: 0, endIndex: 50 },
            groundingChunkIndices: [0, 1]
          }
        ],
        groundingChunks: [
          {
            web: {
              uri: 'https://example.com/ai-definition',
              title: 'AI Definition - Tech Encyclopedia.pdf'
            }
          },
          {
            web: {
              uri: 'https://research.com/ai-overview',
              title: 'AI Overview - Research Paper.pdf'
            }
          }
        ]
      }
    }]
  },

  // Reflection response - sufficient
  reflectionSufficient: {
    candidates: [{
      content: {
        parts: [{
          text: JSON.stringify({
            is_sufficient: true,
            knowledge_gap: '',
            follow_up_queries: []
          })
        }]
      },
      groundingMetadata: {
        groundingSupports: [],
        groundingChunks: []
      }
    }]
  },

  // Reflection response - insufficient
  reflectionInsufficient: {
    candidates: [{
      content: {
        parts: [{
          text: JSON.stringify({
            is_sufficient: false,
            knowledge_gap: 'Need more information about recent AI developments and practical applications',
            follow_up_queries: ['AI developments 2024', 'practical AI applications industry']
          })
        }]
      },
      groundingMetadata: {
        groundingSupports: [],
        groundingChunks: []
      }
    }]
  },

  // Final answer response
  finalAnswer: {
    candidates: [{
      content: {
        parts: [{
          text: 'Based on the research, Artificial Intelligence (AI) represents a transformative technology that simulates human cognitive processes in machines. Current AI systems demonstrate remarkable capabilities in learning, pattern recognition, and decision-making across various domains.'
        }]
      },
      groundingMetadata: {
        groundingSupports: [
          {
            segment: { startIndex: 20, endIndex: 80 },
            groundingChunkIndices: [0, 1, 2]
          }
        ],
        groundingChunks: [
          {
            web: {
              uri: 'https://example.com/ai-research',
              title: 'AI Research 2024.pdf'
            }
          },
          {
            web: {
              uri: 'https://tech.com/ai-applications',
              title: 'AI Applications.pdf'
            }
          },
          {
            web: {
              uri: 'https://science.com/ai-future',
              title: 'Future of AI.pdf'
            }
          }
        ]
      }
    }]
  }
};

// Mock error responses
export const mockErrorResponses = {
  networkError: new Error('Network request failed'),
  apiError: new Error('API key invalid'),
  rateLimitError: new Error('Rate limit exceeded'),
  parseError: new Error('Failed to parse response'),
  
  invalidResponse: {
    candidates: [] // Empty candidates array
  },

  malformedResponse: {
    // Missing required fields
    someField: 'value'
  },

  invalidJson: {
    candidates: [{
      content: {
        parts: [{
          text: 'Invalid JSON: {query: [missing quotes]}'
        }]
      }
    }]
  }
};

// Mock HTTP responses for Express testing
export const mockHttpResponses = {
  healthCheck: {
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  },

  agentRun: {
    id: 'run-123',
    status: 'completed',
    result: {
      answer: 'This is the final answer based on research.',
      sources: testSources.slice(0, 3),
      confidence: 0.89
    },
    metadata: {
      queriesGenerated: 3,
      researchLoops: 2,
      totalSources: 5
    }
  },

  agentStream: [
    { type: 'query_generation', data: { queries: ['query 1', 'query 2'] } },
    { type: 'web_search', data: { query: 'query 1', results: 'Search results...' } },
    { type: 'reflection', data: { is_sufficient: false, follow_up: ['query 3'] } },
    { type: 'final_answer', data: { answer: 'Final answer', confidence: 0.9 } }
  ]
};

// Mock environment variables
export const mockEnvVars = {
  test: {
    NODE_ENV: 'test',
    GEMINI_API_KEY: 'test-api-key-12345',
    QUERY_GENERATOR_MODEL: 'test-gemini-query-model',
    REFLECTION_MODEL: 'test-gemini-reflection-model',
    ANSWER_MODEL: 'test-gemini-answer-model',
    NUMBER_OF_INITIAL_QUERIES: '2',
    MAX_RESEARCH_LOOPS: '1',
    PORT: '3001',
    LOG_LEVEL: 'error'
  },

  development: {
    NODE_ENV: 'development',
    GEMINI_API_KEY: 'dev-api-key',
    QUERY_GENERATOR_MODEL: 'gemini-2.0-flash',
    REFLECTION_MODEL: 'gemini-2.5-flash-preview-04-17',
    ANSWER_MODEL: 'gemini-2.5-pro-preview-05-06',
    NUMBER_OF_INITIAL_QUERIES: '3',
    MAX_RESEARCH_LOOPS: '2',
    PORT: '3000',
    LOG_LEVEL: 'info'
  }
};

// Helper functions for setting up mocks
export const setupGeminiMocks = () => {
  const mockClient = createMockGeminiClient();
  const mockModel = mockClient.getGenerativeModel();
  
  // Default successful responses
  mockModel.generateContent.mockResolvedValue(mockGeminiResponses.webSearch);
  mockModel.withStructuredOutput().invoke.mockResolvedValue({
    query: ['test query'],
    rationale: 'test rationale'
  });

  return { mockClient, mockModel };
};

export const setupLangChainMocks = () => {
  const mockLLM = createMockChatGoogleGenerativeAI();
  
  // Default successful responses
  mockLLM.invoke.mockResolvedValue({
    content: 'Mock LLM response'
  });

  mockLLM.withStructuredOutput().invoke.mockResolvedValue({
    query: ['mock query'],
    rationale: 'mock rationale'
  });

  return mockLLM;
};

export const setupEnvironmentMocks = (env: 'test' | 'development' = 'test') => {
  const envVars = mockEnvVars[env];
  
  // Clear existing env vars
  Object.keys(process.env).forEach(key => {
    if (key.startsWith('GEMINI_') || key.startsWith('QUERY_') || 
        key.startsWith('REFLECTION_') || key.startsWith('ANSWER_') ||
        key.startsWith('NUMBER_') || key.startsWith('MAX_')) {
      delete process.env[key];
    }
  });

  // Set mock env vars
  Object.entries(envVars).forEach(([key, value]) => {
    process.env[key] = value;
  });

  return envVars;
};

// Mock fetch for HTTP requests
export const setupFetchMocks = () => {
  const mockFetch = jest.fn();
  
  // Default successful response
  mockFetch.mockResolvedValue({
    ok: true,
    status: 200,
    json: jest.fn().mockResolvedValue(mockHttpResponses.agentRun),
    text: jest.fn().mockResolvedValue(JSON.stringify(mockHttpResponses.agentRun))
  });

  global.fetch = mockFetch;
  return mockFetch;
};

// Cleanup function for tests
export const cleanupMocks = () => {
  jest.clearAllMocks();
  jest.restoreAllMocks();
  
  // Restore original environment
  setupEnvironmentMocks('test');
};
